'use client';
import { Col, Row } from "react-bootstrap";
import React, { useState, useEffect } from 'react'
import AccountLayout from "@/Layouts/AccountLayout";
import MetaHead from "@/Seo/Meta/MetaHead";
import SidebarHeading from "@/Components/common/Account/SidebarHeading";
import TextInput from "@/Components/UI/TextInput";
import StatusIndicator from "@/Components/UI/StatusIndicator";
import { put, get } from "@/utils/apiUtils";
import { useSelector, useDispatch } from 'react-redux';
import { setUser } from '@/redux/authSlice';
import * as Yup from "yup";
import "@/css/account/AccountDetails.scss";

// Email validation schema
const emailValidationSchema = Yup.object({
    email: Yup.string()
        .trim()
        .required('Email address is required.')
        .email('Please enter a valid email address.')
        .max(255, 'Email address must not exceed 255 characters.')
        .matches(/\.[a-zA-Z]{2,4}$/, 'Please enter a valid email address with a proper domain.'),
});

export default function SetupEmail() {
    const [email, setEmail] = useState('');
    const [originalEmail, setOriginalEmail] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [isLoadingUserData, setIsLoadingUserData] = useState(true);
    const [saveStatus, setSaveStatus] = useState(null);
    const [error, setError] = useState(null);
    const [validationError, setValidationError] = useState(null);

    const dispatch = useDispatch();
    const reduxUser = useSelector((state) => state?.auth?.user || null);

    // Helper function to extract meaningful error messages
    const getErrorMessage = (error) => {
        // Handle validation errors from backend
        if (error.response?.status === 422) {
            const backendErrors = error.response?.data?.errors;
            if (backendErrors?.email) {
                return Array.isArray(backendErrors.email)
                    ? backendErrors.email[0]
                    : backendErrors.email;
            } else if (error.response?.data?.message) {
                return error.response.data.message;
            }
        }

        // Handle other HTTP errors
        if (error.response?.data?.message) {
            return error.response.data.message;
        }

        // Handle network or other errors
        if (error.message) {
            return error.message;
        }

        // Default fallback
        return 'Failed to update email address. Please try again.';
    };

    const metaArray = {
        noindex: true,
        title: "Setup Email Address | Update Info | TradeReply",
        description: "Update your email address on TradeReply.com.",
        canonical_link: "https://www.tradereply.com/account/details",
        og_site_name: "TradeReply",
        og_title: "Setup Email Address | Update Info | TradeReply",
        og_description: "Update your email address on TradeReply.com.",
        twitter_title: "Setup Email Address | Update Info | TradeReply",
        twitter_description: "Update your email address on TradeReply.com.",
    };

    // Fetch user data to pre-populate email
    const fetchUserData = async () => {
        try {
            setIsLoadingUserData(true);
            const response = await get('/account');

            if (response.success && response.data) {
                const existingEmail = response.data.email || '';
                setEmail(existingEmail);
                setOriginalEmail(existingEmail);

                // Update Redux store
                dispatch(setUser(response.data));
                localStorage.setItem('user', JSON.stringify(response.data));
            }
        } catch (error) {
            console.error('Error fetching user data:', error);
            // Fallback to Redux user data
            if (reduxUser?.email) {
                setEmail(reduxUser.email);
                setOriginalEmail(reduxUser.email);
            }
        } finally {
            setIsLoadingUserData(false);
        }
    };

    useEffect(() => {
        fetchUserData();
    }, []);

    // Real-time email validation
    const validateEmail = async (emailValue) => {
        try {
            await emailValidationSchema.validate({ email: emailValue });
            setValidationError(null);
            return true;
        } catch (err) {
            // Provide more user-friendly error messages
            let errorMessage = err.message;
            if (err.message.includes('email')) {
                errorMessage = 'Please enter a valid email address.';
            }
            setValidationError(errorMessage);
            return false;
        }
    };

    const handleEmailChange = (e) => {
        const value = e.target.value;
        setEmail(value);

        // Clear previous errors
        setError(null);
        setSaveStatus(null);

        // Validate email in real-time
        if (value.trim()) {
            validateEmail(value.trim());
        } else {
            setValidationError(null);
        }
    };

    const handleSave = async () => {
        const trimmedEmail = email.trim();

        // Validate email first
        const isValid = await validateEmail(trimmedEmail);
        if (!isValid) {
            setSaveStatus('error');
            setError(validationError || 'Please enter a valid email address.');
            setTimeout(() => setSaveStatus(null), 3000);
            return;
        }

        // Additional validation checks
        if (trimmedEmail.length > 255) {
            setSaveStatus('error');
            setError('Email address must not exceed 255 characters.');
            setTimeout(() => setSaveStatus(null), 3000);
            return;
        }

        // Check if email has actually changed
        if (trimmedEmail === originalEmail) {
            setSaveStatus('error');
            setError('Please enter a different email address to update');
            setTimeout(() => setSaveStatus(null), 3000);
            return;
        }

        try {
            setIsLoading(true);
            setSaveStatus('loading');
            setError(null);
            setValidationError(null);

            const response = await put('/account/email/update', {
                email: trimmedEmail,
            });

            if (response.success) {
                if (response.redirect_to_verification) {
                    // Store verification data and redirect to security-check
                    sessionStorage.setItem('email_update_session_id', response.session_id);
                    sessionStorage.setItem('email_update_masked_email', response.masked_email);
                    sessionStorage.setItem('email_update_new_email', trimmedEmail);

                    // Redirect to security-check with email update context
                    const currentPath = encodeURIComponent('/account/email/setup');
                    router.push(`/security-check?type=email-update&next=${currentPath}`);
                } else {
                    setSaveStatus('success');
                    setOriginalEmail(trimmedEmail);

                    // Update Redux store and localStorage with new email
                    if (reduxUser) {
                        const updatedUser = { ...reduxUser, email: trimmedEmail };
                        dispatch(setUser(updatedUser));
                        localStorage.setItem('user', JSON.stringify(updatedUser));
                    }

                    setTimeout(() => setSaveStatus(null), 3000);
                }
            } else {
                throw new Error(response.message || 'Failed to update email address');
            }
        } catch (err) {
            console.error('Email update error:', err);

            const errorMessage = getErrorMessage(err);
            setSaveStatus('error');
            setError(errorMessage);
            setTimeout(() => setSaveStatus(null), 3000);
        } finally {
            setIsLoading(false);
        }
    };

    const handleCancel = () => {
        setEmail(originalEmail);
        setError(null);
        setValidationError(null);
        setSaveStatus(null);
    };

    // Check if form has changes
    const hasChanges = email.trim() !== originalEmail;
    const isFormDisabled = isLoading || isLoadingUserData;
    return (
        <>
            <AccountLayout>
                <MetaHead props={metaArray} />
                <div className="account_setup_email">
                    <SidebarHeading title="New Email Address" />
                    <div className="mb-3 mb-lg-4">
                        <div className="common_blackcard account_card">
                            <div className="common_blackcard_innerheader">
                                <div className="common_blackcard_innerheader_content">
                                    <div className="account_header_main">
                                        <h6>Email</h6>
                                        <div className="account_status_indicator">
                                            <StatusIndicator
                                                saveStatus={saveStatus}
                                            />
                                        </div>
                                    </div>
                                    <p>Enter the email address you want associated with your TradeReply account. You will use this email address to log in.</p>
                                    {originalEmail && (
                                        <p style={{ fontSize: '14px', color: '#888', marginTop: '8px' }}>
                                            Current email: {originalEmail}
                                        </p>
                                    )}
                                </div>
                            </div>
                            <div className="common_blackcard_innerbody">
                                <div className="account_card_list">
                                    <div className="col-lg-5 col-md-8 col-12">
                                        <TextInput
                                            type="email"
                                            placeholder={isLoadingUserData ? "Loading..." : "Enter your email address"}
                                            value={email}
                                            onChange={handleEmailChange}
                                            disabled={isFormDisabled}
                                        />
                                    </div>

                                    {(validationError || (error && saveStatus !== 'loading')) && (
                                        <div className="mt-3">
                                            <p style={{ color: 'red', fontSize: '14px' }}>
                                                {validationError || error}
                                            </p>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>
                        <div className="account_card_list_btns mt-3">
                            <button
                                className="btn-style white-btn"
                                onClick={handleCancel}
                                disabled={isFormDisabled || !hasChanges}
                            >
                                Cancel
                            </button>
                            <button
                                className="btn-style"
                                onClick={handleSave}
                                disabled={isFormDisabled || !email.trim() || !hasChanges || validationError}
                            >
                                {isLoading ? 'Updating...' : 'Update Email'}
                            </button>
                        </div>
                    </div>

                </div>
            </AccountLayout>
        </>
    )
}
